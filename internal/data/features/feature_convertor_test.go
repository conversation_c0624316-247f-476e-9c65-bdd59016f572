package features

import (
	"testing"
)

// TestConvertValue tests the ConvertValue function with various inputs and feature types.
func TestConvertValue(t *testing.T) {
	tests := []struct {
		name         string
		rawValue     string
		featureType  FeatureType
		expectedVal  any
		expectedType string
		wantErr      bool
		errContains  string
	}{
		// Integer feature tests
		{
			name:         "valid integer conversion",
			rawValue:     "123",
			featureType:  IntegerFeature,
			expectedVal:  int64(123),
			expectedType: "int64",
			wantErr:      false,
		},
		{
			name:         "negative integer conversion",
			rawValue:     "-456",
			featureType:  IntegerFeature,
			expectedVal:  int64(-456),
			expectedType: "int64",
			wantErr:      false,
		},
		{
			name:         "zero integer conversion",
			rawValue:     "0",
			featureType:  IntegerFeature,
			expectedVal:  int64(0),
			expectedType: "int64",
			wantErr:      false,
		},
		{
			name:         "integer with whitespace",
			rawValue:     "  789  ",
			featureType:  IntegerFeature,
			expectedVal:  int64(789),
			expectedType: "int64",
			wantErr:      false,
		},
		{
			name:        "invalid integer conversion",
			rawValue:    "abc",
			featureType: IntegerFeature,
			wantErr:     true,
			errContains: "cannot convert",
		},
		{
			name:        "float as integer",
			rawValue:    "123.45",
			featureType: IntegerFeature,
			wantErr:     true,
			errContains: "cannot convert",
		},

		// Float feature tests
		{
			name:         "valid float conversion",
			rawValue:     "123.45",
			featureType:  FloatFeature,
			expectedVal:  float64(123.45),
			expectedType: "float64",
			wantErr:      false,
		},
		{
			name:         "negative float conversion",
			rawValue:     "-456.78",
			featureType:  FloatFeature,
			expectedVal:  float64(-456.78),
			expectedType: "float64",
			wantErr:      false,
		},
		{
			name:         "integer as float",
			rawValue:     "123",
			featureType:  FloatFeature,
			expectedVal:  float64(123),
			expectedType: "float64",
			wantErr:      false,
		},
		{
			name:         "zero float conversion",
			rawValue:     "0.0",
			featureType:  FloatFeature,
			expectedVal:  float64(0.0),
			expectedType: "float64",
			wantErr:      false,
		},
		{
			name:         "float with whitespace",
			rawValue:     "  3.14159  ",
			featureType:  FloatFeature,
			expectedVal:  float64(3.14159),
			expectedType: "float64",
			wantErr:      false,
		},
		{
			name:        "invalid float conversion",
			rawValue:    "abc",
			featureType: FloatFeature,
			wantErr:     true,
			errContains: "cannot convert",
		},

		// String feature tests
		{
			name:         "valid string conversion",
			rawValue:     "sunny",
			featureType:  StringFeature,
			expectedVal:  "sunny",
			expectedType: "string",
			wantErr:      false,
		},
		{
			name:         "string with whitespace",
			rawValue:     "  cloudy  ",
			featureType:  StringFeature,
			expectedVal:  "cloudy",
			expectedType: "string",
			wantErr:      false,
		},
		{
			name:         "numeric string",
			rawValue:     "123",
			featureType:  StringFeature,
			expectedVal:  "123",
			expectedType: "string",
			wantErr:      false,
		},
		{
			name:         "empty string after trim",
			rawValue:     "   ",
			featureType:  StringFeature,
			wantErr:      true,
			errContains:  "missing value",
		},

		// Edge cases
		{
			name:        "empty string",
			rawValue:    "",
			featureType: IntegerFeature,
			wantErr:     true,
			errContains: "missing value",
		},
		{
			name:        "whitespace only",
			rawValue:    "   ",
			featureType: FloatFeature,
			wantErr:     true,
			errContains: "missing value",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ConvertValue(tt.rawValue, tt.featureType)

			if tt.wantErr {
				if err == nil {
					t.Errorf("ConvertValue() expected error but got none")
					return
				}
				if tt.errContains != "" && !containsString(err.Error(), tt.errContains) {
					t.Errorf("ConvertValue() error = %v, expected to contain %v", err, tt.errContains)
				}
				return
			}

			if err != nil {
				t.Errorf("ConvertValue() unexpected error = %v", err)
				return
			}

			if result != tt.expectedVal {
				t.Errorf("ConvertValue() = %v (%T), expected %v (%T)", result, result, tt.expectedVal, tt.expectedVal)
			}

			// Verify type
			switch tt.expectedType {
			case "int64":
				if _, ok := result.(int64); !ok {
					t.Errorf("ConvertValue() result type = %T, expected int64", result)
				}
			case "float64":
				if _, ok := result.(float64); !ok {
					t.Errorf("ConvertValue() result type = %T, expected float64", result)
				}
			case "string":
				if _, ok := result.(string); !ok {
					t.Errorf("ConvertValue() result type = %T, expected string", result)
				}
			}
		})
	}
}

// TestConvertValueWithFallback tests the ConvertValueWithFallback function.
func TestConvertValueWithFallback(t *testing.T) {
	tests := []struct {
		name            string
		rawValue        string
		featureType     FeatureType
		allowFallback   bool
		expectedVal     any
		expectedType    FeatureType
		wantErr         bool
		errContains     string
	}{
		// Successful conversions (no fallback needed)
		{
			name:          "successful integer conversion",
			rawValue:      "123",
			featureType:   IntegerFeature,
			allowFallback: false,
			expectedVal:   int64(123),
			expectedType:  IntegerFeature,
			wantErr:       false,
		},
		{
			name:          "successful float conversion",
			rawValue:      "123.45",
			featureType:   FloatFeature,
			allowFallback: true,
			expectedVal:   float64(123.45),
			expectedType:  FloatFeature,
			wantErr:       false,
		},

		// Fallback scenarios
		{
			name:          "integer fallback to string",
			rawValue:      "abc",
			featureType:   IntegerFeature,
			allowFallback: true,
			expectedVal:   "abc",
			expectedType:  StringFeature,
			wantErr:       false,
		},
		{
			name:          "float fallback to string",
			rawValue:      "xyz",
			featureType:   FloatFeature,
			allowFallback: true,
			expectedVal:   "xyz",
			expectedType:  StringFeature,
			wantErr:       false,
		},

		// No fallback allowed
		{
			name:          "integer no fallback",
			rawValue:      "abc",
			featureType:   IntegerFeature,
			allowFallback: false,
			wantErr:       true,
			errContains:   "cannot convert",
		},
		{
			name:          "float no fallback",
			rawValue:      "xyz",
			featureType:   FloatFeature,
			allowFallback: false,
			wantErr:       true,
			errContains:   "cannot convert",
		},

		// String feature (no fallback needed)
		{
			name:          "string feature success",
			rawValue:      "test",
			featureType:   StringFeature,
			allowFallback: true,
			expectedVal:   "test",
			expectedType:  StringFeature,
			wantErr:       false,
		},
		{
			name:          "string feature failure",
			rawValue:      "   ",
			featureType:   StringFeature,
			allowFallback: true,
			wantErr:       true,
			errContains:   "missing value",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, resultType, err := ConvertValueWithFallback(tt.rawValue, tt.featureType, tt.allowFallback)

			if tt.wantErr {
				if err == nil {
					t.Errorf("ConvertValueWithFallback() expected error but got none")
					return
				}
				if tt.errContains != "" && !containsString(err.Error(), tt.errContains) {
					t.Errorf("ConvertValueWithFallback() error = %v, expected to contain %v", err, tt.errContains)
				}
				return
			}

			if err != nil {
				t.Errorf("ConvertValueWithFallback() unexpected error = %v", err)
				return
			}

			if result != tt.expectedVal {
				t.Errorf("ConvertValueWithFallback() result = %v, expected %v", result, tt.expectedVal)
			}

			if resultType != tt.expectedType {
				t.Errorf("ConvertValueWithFallback() type = %v, expected %v", resultType, tt.expectedType)
			}
		})
	}
}

// TestValidateConvertedValue tests the ValidateConvertedValue function.
func TestValidateConvertedValue(t *testing.T) {
	tests := []struct {
		name         string
		value        any
		expectedType FeatureType
		expected     bool
	}{
		// Valid type matches
		{
			name:         "valid int64 for IntegerFeature",
			value:        int64(123),
			expectedType: IntegerFeature,
			expected:     true,
		},
		{
			name:         "valid float64 for FloatFeature",
			value:        float64(123.45),
			expectedType: FloatFeature,
			expected:     true,
		},
		{
			name:         "valid string for StringFeature",
			value:        "test",
			expectedType: StringFeature,
			expected:     true,
		},

		// Invalid type matches
		{
			name:         "int for FloatFeature",
			value:        int64(123),
			expectedType: FloatFeature,
			expected:     false,
		},
		{
			name:         "float for IntegerFeature",
			value:        float64(123.45),
			expectedType: IntegerFeature,
			expected:     false,
		},
		{
			name:         "string for IntegerFeature",
			value:        "test",
			expectedType: IntegerFeature,
			expected:     false,
		},

		// Nil value
		{
			name:         "nil value",
			value:        nil,
			expectedType: IntegerFeature,
			expected:     false,
		},

		// Invalid feature type
		{
			name:         "invalid feature type",
			value:        int64(123),
			expectedType: FeatureType(999),
			expected:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ValidateConvertedValue(tt.value, tt.expectedType)
			if result != tt.expected {
				t.Errorf("ValidateConvertedValue() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

// Helper function to check if a string contains a substring
func containsString(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 || 
		(len(s) > len(substr) && findSubstring(s, substr)))
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
