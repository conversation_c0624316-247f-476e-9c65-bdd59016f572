package features

import (
	"testing"
)

// TestTryConvertValue tests the TryConvertValue function with various inputs and feature types.

// TestValidateConvertedValue tests the ValidateConvertedValue function.
func TestValidateConvertedValue(t *testing.T) {
	tests := []struct {
		name         string
		value        any
		expectedType FeatureType
		expected     bool
	}{
		// Valid type matches
		{
			name:         "valid int64 for IntegerFeature",
			value:        int64(123),
			expectedType: IntegerFeature,
			expected:     true,
		},
		{
			name:         "valid float64 for FloatFeature",
			value:        float64(123.45),
			expectedType: FloatFeature,
			expected:     true,
		},
		{
			name:         "valid string for StringFeature",
			value:        "test",
			expectedType: StringFeature,
			expected:     true,
		},

		// Invalid type matches
		{
			name:         "int for FloatFeature",
			value:        int64(123),
			expectedType: FloatFeature,
			expected:     false,
		},
		{
			name:         "float for IntegerFeature",
			value:        float64(123.45),
			expectedType: IntegerFeature,
			expected:     false,
		},
		{
			name:         "string for IntegerFeature",
			value:        "test",
			expectedType: IntegerFeature,
			expected:     false,
		},

		// Nil value
		{
			name:         "nil value",
			value:        nil,
			expectedType: IntegerFeature,
			expected:     false,
		},

		// Invalid feature type
		{
			name:         "invalid feature type",
			value:        int64(123),
			expectedType: FeatureType(999),
			expected:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ValidateConvertedValue(tt.value, tt.expectedType)
			if result != tt.expected {
				t.Errorf("ValidateConvertedValue() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

