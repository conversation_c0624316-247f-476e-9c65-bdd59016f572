package features

import (
	"fmt"
	"strconv"
	"strings"
)

// FeatureConvertor handles conversion of feature values to internal types.
//
// Args: None (interface definition)
// Constraints: All methods must be implemented by concrete types
// Security: No sensitive data handling
// Relationships: Used by Dataset to convert raw CSV values to internal types
// Side effects: None (pure conversion functions)

// ConvertValue converts a raw CSV string value to the appropriate internal type based on FeatureType.
//
// Args:
// - rawValue: Raw string value from CSV (may contain whitespace)
// - featureType: Target internal type (IntegerFeature/FloatFeature/StringFeature)
//
// Returns:
// - any: Converted value (int64, float64, or string)
// - error: Conversion error if value cannot be parsed to target type
//
// Security: Validates input and handles conversion errors gracefully
// Performance: O(1) string parsing operations
// Relationships: Used during CSV loading to convert raw strings to typed values
// Side effects: None (pure function)
//
// Examples:
//
//	ConvertValue("123", IntegerFeature) -> int64(123), nil
//	ConvertValue("45.67", FloatFeature) -> float64(45.67), nil
//	ConvertValue("sunny", StringFeature) -> "sunny", nil
//	ConvertValue("invalid", IntegerFeature) -> nil, error
func ConvertValue(rawValue string, featureType FeatureType) (any, error) {
	// Handle empty/whitespace values as missing
	trimmedValue := strings.TrimSpace(rawValue)
	if trimmedValue == "" {
		return nil, fmt.Errorf("missing value")
	}

	switch featureType {
	case IntegerFeature:
		// Convert to int64 for integer features
		intVal, err := strconv.ParseInt(trimmedValue, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("cannot convert '%s' to integer: %w", rawValue, err)
		}
		return intVal, nil

	case FloatFeature:
		// Convert to float64 for float features
		floatVal, err := strconv.ParseFloat(trimmedValue, 64)
		if err != nil {
			return nil, fmt.Errorf("cannot convert '%s' to float: %w", rawValue, err)
		}
		return floatVal, nil

	case StringFeature:
		// Return trimmed string for string features
		return trimmedValue, nil

	default:
		return nil, fmt.Errorf("unsupported feature type: %v", featureType)
	}
}

func ValidateConvertedValue(value any, expectedType FeatureType) bool {
	if value == nil {
		return false
	}

	switch expectedType {
	case IntegerFeature:
		_, ok := value.(int64)
		return ok
	case FloatFeature:
		_, ok := value.(float64)
		return ok
	case StringFeature:
		_, ok := value.(string)
		return ok
	default:
		return false
	}
}
